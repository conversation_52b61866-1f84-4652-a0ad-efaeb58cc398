package com.synergy.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Data transfer object for gate pass details")
public class GatePassDTO {
    
    @Schema(description = "Gate Pass ID", example = "PO647-1-GP01")
    private String gatePassId;
    
    @Schema(description = "Vendor PO ID", example = "647-1")
    private String vendorPoId;
    
    @Schema(description = "Original Purchase Request ID", example = "647")
    private Long originalPrId;
    
    @Schema(description = "Original Purchase Request ID string", example = "PR-20250101-0647")
    private String originalPrIdString;
    
    @Schema(description = "Vendor ID", example = "15")
    private Long vendorId;
    
    @Schema(description = "Vendor company name", example = "ABC Steel Suppliers")
    private String vendorCompanyName;
    
    @Schema(description = "Vendor contact name", example = "<PERSON>")
    private String vendorName;
    
    @Schema(description = "Vendor email", example = "<EMAIL>")
    private String vendorEmail;
    
    @Schema(description = "Vendor contact number", example = "+91-9876543210")
    private String vendorContactNumber;
    
    @Schema(description = "Yard number", example = "YD-101")
    private String yardNumber;
    
    @Schema(description = "Project name", example = "Speed Boat")
    private String projectName;
    
    @Schema(description = "Contractor/Client name", example = "Oceanic Shipbuilders")
    private String contractorName;
    
    @Schema(description = "Expected delivery date", example = "2025-03-19T00:00:00.000Z")
    private Date expectedDate;
    
    @Schema(description = "Number of line items", example = "10")
    private Integer lineItemCount;
    
    @Schema(description = "Gate pass status", example = "PENDING")
    private String status;
    
    @Schema(description = "Date when gate pass was created", example = "2025-01-15T10:30:00.000Z")
    private Date createdDate;
    
    @Schema(description = "Date when gate pass was issued", example = "2025-01-16T14:20:00.000Z")
    private Date issuedDate;
    
    @Schema(description = "Date when gate pass was completed", example = "2025-01-17T16:45:00.000Z")
    private Date completedDate;

    @Schema(description = "Remarks or notes", example = "Urgent delivery required")
    private String remarks;

    @Schema(description = "Date until which gate pass is editable (creation time + 3 hours)", example = "2025-01-15T13:30:00.000Z")
    private Date validTill;

    @Schema(description = "Whether the gate pass is currently editable", example = "true")
    private Boolean isEditable;
}
