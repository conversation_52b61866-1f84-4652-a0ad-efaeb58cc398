package com.synergy.controller;

import com.synergy.dto.GatePassDTO;
import com.synergy.dto.GatePassLightDTO;
import com.synergy.dto.GatePassFormDTO;
import com.synergy.entity.GatePassEntity;
import com.synergy.service.GatePassService;
import com.synergy.service.FileStorageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/gate-passes")
@CrossOrigin(origins = "*", allowedHeaders = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.OPTIONS})
@Tag(name = "Gate Pass Management", description = "APIs for managing gate passes for approved purchase orders")
public class GatePassController {

    @Autowired
    private GatePassService gatePassService;

    @Autowired
    private FileStorageService fileStorageService;

    /**
     * Get all approved vendor POs available for gate pass creation
     */
    @GetMapping("/available")
    @Operation(
        summary = "Get approved vendor POs available for gate pass creation",
        description = "Retrieves all approved vendor POs that can have gate passes created. " +
                "Shows the same data structure as approved vendor POs but formatted for gate pass dashboard. " +
                "Only shows vendor POs that don't already have gate passes created.",
        tags = {"Gate Pass Management"}
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Available vendor POs retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = GatePassLightDTO.class))
        ),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getAvailableVendorPOsForGatePass() {
        try {
            List<GatePassLightDTO> availableGatePasses = gatePassService.getAllApprovedVendorPOsForGatePassLight();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", availableGatePasses);
            response.put("count", availableGatePasses.size());
            response.put("message", "Available vendor POs for gate pass creation retrieved successfully");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to retrieve available vendor POs: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get all existing gate passes
     */
    @GetMapping
    @Operation(
        summary = "Get all gate passes",
        description = "Retrieves all existing gate passes ordered by creation date (newest first).",
        tags = {"Gate Pass Management"}
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Gate passes retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = GatePassDTO.class))
        ),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getAllGatePasses() {
        try {
            List<GatePassDTO> gatePasses = gatePassService.getAllGatePasses();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", gatePasses);
            response.put("count", gatePasses.size());
            response.put("message", "Gate passes retrieved successfully");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to retrieve gate passes: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get gate passes by status
     */
    @GetMapping("/by-status")
    @Operation(
        summary = "Get gate passes by status",
        description = "Retrieves gate passes filtered by status (PENDING, ISSUED, COMPLETED) ordered by expected date.",
        tags = {"Gate Pass Management"}
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Gate passes retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = GatePassDTO.class))
        ),
        @ApiResponse(responseCode = "400", description = "Invalid status parameter"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getGatePassesByStatus(
            @Parameter(description = "Status to filter by (PENDING, ISSUED, COMPLETED)", required = true, example = "PENDING")
            @RequestParam String status) {
        try {
            List<GatePassDTO> gatePasses = gatePassService.getGatePassesByStatus(status);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", gatePasses);
            response.put("count", gatePasses.size());
            response.put("status", status);
            response.put("message", "Gate passes with status '" + status + "' retrieved successfully");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to retrieve gate passes: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Create a gate pass for a vendor PO
     */
    @PostMapping("/create/{vendorPoId}")
    @Operation(
        summary = "Create a gate pass for a vendor PO",
        description = "Creates a new gate pass for the specified vendor PO. " +
                "Gate pass ID will be generated in format: PO{vendorPoId}-GP{sequence}. " +
                "Expected date will be taken from the delivery terms used during PDF generation.",
        tags = {"Gate Pass Management"}
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Gate pass created successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = GatePassDTO.class))
        ),
        @ApiResponse(responseCode = "400", description = "Gate pass already exists or invalid vendor PO"),
        @ApiResponse(responseCode = "404", description = "Vendor PO not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> createGatePass(
            @Parameter(description = "Vendor PO ID (e.g., 647 for single vendor, 647-1 for multiple vendors)", required = true, example = "647")
            @PathVariable String vendorPoId) {
        try {
            GatePassDTO createdGatePass = gatePassService.createGatePass(vendorPoId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", createdGatePass);
            response.put("message", "Gate pass created successfully with ID: " + createdGatePass.getGatePassId());

            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to create gate pass: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get gate pass form data for creating a gate pass
     */
    @GetMapping("/{vendorPoId}/form")
    @Operation(
        summary = "Get gate pass form data",
        description = "Retrieves form data including line items for creating a gate pass",
        tags = {"Gate Pass Management"}
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Form data retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Vendor PO not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getGatePassFormData(
            @Parameter(description = "Vendor PO ID") @PathVariable String vendorPoId) {
        try {
            GatePassFormDTO formData = gatePassService.getGatePassFormData(vendorPoId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", formData);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to fetch form data: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Submit gate pass form with photos
     */
    @PostMapping("/{vendorPoId}/submit")
    @Operation(
        summary = "Submit gate pass form",
        description = "Creates a gate pass with form data including vehicle photos",
        tags = {"Gate Pass Management"}
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Gate pass created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> submitGatePassForm(
            @Parameter(description = "Vendor PO ID") @PathVariable String vendorPoId,
            @RequestBody Map<String, Object> requestData) {
        try {
            GatePassEntity createdGatePass = gatePassService.submitGatePassForm(vendorPoId, requestData);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Gate pass created successfully");
            response.put("data", Map.of(
                "gatePassId", createdGatePass.getGatePassId(),
                "status", createdGatePass.getStatus(),
                "createdDate", createdGatePass.getCreatedDate(),
                "issuedDate", createdGatePass.getIssuedDate()
            ));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to create gate pass: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Get issued gate passes for the issued dashboard
     */
    @GetMapping("/issued")
    @Operation(
        summary = "Get issued gate passes",
        description = "Retrieves all issued gate passes for the issued dashboard",
        tags = {"Gate Pass Management"}
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Issued gate passes retrieved successfully"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getIssuedGatePasses() {
        try {
            List<Map<String, Object>> issuedGatePasses = gatePassService.getIssuedGatePassesWithPhotos();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", issuedGatePasses);
            response.put("count", issuedGatePasses.size());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to fetch issued gate passes: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Get gate pass by ID
     */
    @GetMapping("/{gatePassId}")
    @Operation(
        summary = "Get gate pass by ID",
        description = "Retrieves a specific gate pass by its ID",
        tags = {"Gate Pass Management"}
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Gate pass retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Gate pass not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getGatePassById(
            @Parameter(description = "Gate Pass ID", required = true, example = "PO647-GP01")
            @PathVariable String gatePassId) {
        try {
            GatePassDTO gatePass = gatePassService.getGatePassById(gatePassId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", gatePass);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to retrieve gate pass: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Update gate pass
     */
    @PutMapping("/{gatePassId}")
    @Operation(
        summary = "Update gate pass",
        description = "Updates a gate pass with new information. Only editable within 3 hours of creation.",
        tags = {"Gate Pass Management"}
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Gate pass updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input or gate pass not editable"),
        @ApiResponse(responseCode = "404", description = "Gate pass not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> updateGatePass(
            @Parameter(description = "Gate Pass ID", required = true, example = "PO647-GP01")
            @PathVariable String gatePassId,
            @RequestBody Map<String, Object> updateData) {
        try {
            GatePassDTO updatedGatePass = gatePassService.updateGatePass(gatePassId, updateData);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", updatedGatePass);
            response.put("message", "Gate pass updated successfully");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to update gate pass: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Delete gate pass
     */
    @DeleteMapping("/{gatePassId}")
    @Operation(
        summary = "Delete gate pass",
        description = "Deletes a gate pass. Only gate passes with PENDING status can be deleted.",
        tags = {"Gate Pass Management"}
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Gate pass deleted successfully"),
        @ApiResponse(responseCode = "400", description = "Gate pass cannot be deleted"),
        @ApiResponse(responseCode = "404", description = "Gate pass not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> deleteGatePass(
            @Parameter(description = "Gate Pass ID", required = true, example = "PO647-GP01")
            @PathVariable String gatePassId) {
        try {
            gatePassService.deleteGatePass(gatePassId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Gate pass deleted successfully");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to delete gate pass: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
